import os, base64, json, requests, subprocess, tempfile
from typing import Dict, Any, List
from openai import OpenAI

# AI Configuration
AI_PROVIDER = "auggie"  # Options: "openai" or "auggie"
OPENAI_MODEL = "gpt-4o-mini"  # pick yours
AUGGIE_CDOS_PATH = r"C:\Users\<USER>\PycharmProjects\CDOS"  # Path to CDOS directory
AUGGIE_COMMAND = "auggie"  # Command to run auggie

# os.environ["OPENAI_API_KEY"]
# os.environ["JIRA_BASE_URL"].rstrip("/")
# os.environ["JIRA_EMAIL"]
# os.environ["JIRA_API_TOKEN"]
# os.environ["JIRA_PROJECT_KEY"]

# Initialize OpenAI client (only used if AI_PROVIDER is "openai")
openai_client = None
if AI_PROVIDER == "openai":
    openai_client = OpenAI(
        api_key="********************************************************************************************************************************************************************")

JIRA = {
    "base": "https://tradecraft1.atlassian.net",
    "email": "<EMAIL>",
    "token": "ATATT3xFfGF0Vs9ziGNK3A0Pp1JtKZ4tQuO5rInrIh9UvbYL_xM--S-CDaBYMXT2yYVihrls3oQO5WRd5B5Ep3HBlQoDm-TVG_YtYKhN_48vtqT_B5VEJGfrbwTQWGcpfHDtXC7f1iGmxxH7vnAnSrJ02S8Pp56odyGgynJrCADTF7xk-QUa2II=BA9D1004",
    "project": "CCS",
}


def _auth_header() -> Dict[str, str]:
    b = base64.b64encode(f'{JIRA["email"]}:{JIRA["token"]}'.encode()).decode()
    return {"Authorization": f"Basic {b}", "Accept": "application/json", "Content-Type": "application/json"}


def _call_auggie(prompt: str) -> str:
    """Call locally installed Auggie with the given prompt"""
    temp_file = None
    try:
        # Verify CDOS directory exists
        if not os.path.exists(AUGGIE_CDOS_PATH):
            raise Exception(f"CDOS directory not found: {AUGGIE_CDOS_PATH}")

        print(f"Calling Auggie from CDOS directory: {AUGGIE_CDOS_PATH}")
        print(f"Prompt length: {len(prompt)} characters")

        # Write prompt to temporary file to avoid quote escaping issues
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as f:
            f.write(prompt)
            temp_file = f.name

        # Use PowerShell to run auggie with instruction file
        powershell_cmd = f'cd "{AUGGIE_CDOS_PATH}"; {AUGGIE_COMMAND} --print --instruction-file "{temp_file}"'

        result = subprocess.run(
            ["powershell", "-Command", powershell_cmd],
            capture_output=True,
            text=True,
            timeout=180,  # 3 minute timeout for Auggie
        )

        if result.returncode == 0:
            print("Auggie completed successfully")
            response = result.stdout.strip()
            if response:
                return response
            else:
                raise Exception("Auggie returned empty response")
        else:
            print(f"Auggie error (return code {result.returncode})")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            raise Exception(f"Auggie failed with return code {result.returncode}: {result.stderr}")

    except subprocess.TimeoutExpired:
        raise Exception("Auggie call timed out after 3 minutes")
    except FileNotFoundError:
        raise Exception(f"PowerShell not found. Make sure PowerShell is available.")
    except Exception as e:
        raise Exception(f"Error calling Auggie: {str(e)}")
    finally:
        # Clean up temporary file
        if temp_file and os.path.exists(temp_file):
            try:
                os.unlink(temp_file)
            except:
                pass  # Ignore cleanup errors


def _call_openai(prompt: str) -> str:
    """Call OpenAI API with the given prompt"""
    if not openai_client:
        raise Exception("OpenAI client not initialized. Set AI_PROVIDER to 'openai' to use OpenAI.")

    resp = openai_client.chat.completions.create(
        model=OPENAI_MODEL,
        messages=[
            {"role": "system", "content": "You turn product ideas into a concise Jira plan. Return STRICT JSON only in the given schema. No commentary."},
            {"role": "user", "content": prompt}
        ],
        temperature=0.1,
    )
    return resp.choices[0].message.content


def ai_plan(text: str) -> Dict[str, Any]:
    schema_prompt = """You turn product ideas into a concise Jira plan.
Return STRICT JSON only in this exact schema. No commentary.

{
  "feature": "Feature name",
  "epic": {
    "id": "EPIC-1",
    "summary": "Epic summary",
    "description": "Epic description",
    "priority": "High"
  },
  "issues": [
    {
      "id": "TASK-1",
      "type": "Story",
      "summary": "Task summary",
      "description": "Task description",
      "priority": "High",
      "labels": ["label1", "label2"],
      "parent": "EPIC-1"
    }
  ]
}

Valid issue types: Story, Task, Bug, Sub-task
Valid priorities: Highest, High, Medium, Low

Feature request: """ + text

    # Use the selected AI provider
    if AI_PROVIDER == "auggie":
        response_text = _call_auggie(schema_prompt)
    elif AI_PROVIDER == "openai":
        response_text = _call_openai(schema_prompt)
    else:
        raise Exception(f"Unknown AI provider: {AI_PROVIDER}. Use 'auggie' or 'openai'.")

    try:
        # Clean up the response - remove markdown code blocks and emojis
        cleaned_response = response_text

        # Find and extract JSON from markdown code blocks
        if '```json' in cleaned_response.lower():
            # Find the start of JSON block
            start_marker = '```json'
            start_idx = cleaned_response.lower().find(start_marker)
            if start_idx != -1:
                # Find the end of JSON block
                json_start = cleaned_response.find('\n', start_idx) + 1
                end_marker = '```'
                end_idx = cleaned_response.find(end_marker, json_start)
                if end_idx != -1:
                    cleaned_response = cleaned_response[json_start:end_idx].strip()
                else:
                    # No closing ```, take everything after ```json
                    cleaned_response = cleaned_response[json_start:].strip()
        else:
            # Look for JSON starting with {
            lines = cleaned_response.split('\n')
            json_start_idx = -1
            for i, line in enumerate(lines):
                if line.strip().startswith('{'):
                    json_start_idx = i
                    break

            if json_start_idx >= 0:
                cleaned_response = '\n'.join(lines[json_start_idx:]).strip()

        # Remove any remaining ``` at the end
        if cleaned_response.endswith('```'):
            cleaned_response = cleaned_response[:-3].strip()

        return json.loads(cleaned_response)
    except json.JSONDecodeError as e:
        print(f"Failed to parse AI response as JSON: {e}")
        print(f"Raw response: {response_text}")
        print(f"Cleaned response: {cleaned_response}")
        raise Exception(f"AI returned invalid JSON: {str(e)}")


def _jira_fields_lookup() -> Dict[str, Any]:
    r = requests.get(f'{JIRA["base"]}/rest/api/3/field', headers=_auth_header(), timeout=20)
    r.raise_for_status()
    fields = r.json()
    epic_link_id = None
    for f in fields:
        if f.get("name") in ("Epic Link", "epic link"):
            epic_link_id = f["id"]
            break
    return {"epic_link_id": epic_link_id}


def _adf(text: str) -> Dict[str, Any]:
    return {"type": "doc", "version": 1,
            "content": [{"type": "paragraph", "content": [{"type": "text", "text": text}]}]}


def _create_issue(type_name: str, summary: str, description: str, priority: str, labels: List[str],
                  extra_fields: Dict[str, Any]) -> Dict[str, Any]:
    # Clean labels - remove spaces and special characters
    clean_labels = []
    if labels:
        for label in labels:
            clean_label = label.replace(" ", "-").replace("_", "-")
            # Remove any other special characters that might cause issues
            clean_label = "".join(c for c in clean_label if c.isalnum() or c == "-")
            if clean_label:
                clean_labels.append(clean_label)

    payload = {
        "fields": {
                      "project": {"key": JIRA["project"]},
                      "issuetype": {"name": type_name},
                      "summary": summary[:254],
                      "description": _adf(description),
                      "labels": clean_labels
                  } | (extra_fields or {})
    }

    # Skip priority for now - some projects don't allow priority setting
    # if priority and priority in ("Highest", "High", "Medium", "Low"):
    #     payload["fields"]["priority"] = {"name": priority}
    r = requests.post(f'{JIRA["base"]}/rest/api/3/issue', headers=_auth_header(), json=payload, timeout=30)
    if r.status_code != 201:
        print(f"Error creating issue: {r.status_code}")
        print(f"Response: {r.text}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
    r.raise_for_status()
    return r.json()  # contains key, id, self


def create_from_plan(plan: Dict[str, Any]) -> Dict[str, Any]:
    lookup = _jira_fields_lookup()
    epic_key = None
    local_to_key: Dict[str, str] = {}
    results = {"created": []}

    # 1) Epic (optional)
    if plan.get("epic"):
        e = plan["epic"]
        resp = _create_issue(
            "Epic", e["summary"], e.get("description", ""), e.get("priority", "Medium"), e.get("labels", []),
            extra_fields={}
        )
        epic_key = resp["key"]
        local_to_key[e["id"]] = epic_key
        results["created"].append({"local_id": e["id"], "key": epic_key})

    # 2) Create non-subtasks (Stories/Tasks/Bugs) without parents first
    pending: List[Dict[str, Any]] = []
    for it in plan["issues"]:
        if it["type"] == "Sub-task":
            pending.append(it)
            continue

        extra = {}
        # Link to epic (company-managed projects use Epic Link custom field)
        if epic_key and lookup["epic_link_id"] and it.get("parent") in (plan.get("epic", {}) or {}).get("id", ""):
            extra[lookup["epic_link_id"]] = epic_key

        resp = _create_issue(it["type"], it["summary"], it.get("description", ""), it.get("priority", "Medium"),
                             it.get("labels", []), extra)
        local_to_key[it["id"]] = resp["key"]
        results["created"].append({"local_id": it["id"], "key": resp["key"]})

    # 3) Create Sub-tasks (must use parent= story/task key)
    for it in pending:
        parent_key = local_to_key[it["parent"]]
        resp = _create_issue("Sub-task", it["summary"], it.get("description", ""), it.get("priority", "Medium"),
                             it.get("labels", []),
                             extra_fields={"parent": {"key": parent_key}})
        local_to_key[it["id"]] = resp["key"]
        results["created"].append({"local_id": it["id"], "key": resp["key"]})

    # (Optional) add dependency links
    for it in plan["issues"]:
        if it.get("dependencies"):
            inward = local_to_key[it["id"]]
            for dep_local in it["dependencies"]:
                outward = local_to_key[dep_local]
                requests.post(
                    f'{JIRA["base"]}/rest/api/3/issueLink',
                    headers=_auth_header(),
                    json={"type": {"name": "Blocks"}, "inwardIssue": {"key": inward}, "outwardIssue": {"key": outward}},
                    timeout=20,
                )

    return results


def draft_and_create(user_text: str) -> Dict[str, Any]:
    plan = ai_plan(user_text)
    print("AI Plan:")
    print(json.dumps(plan, indent=2))
    return {"plan": plan, "result": create_from_plan(plan)}


def test_ai_provider():
    """Test the configured AI provider"""
    print(f"AI Provider: {AI_PROVIDER}")

    if AI_PROVIDER == "auggie":
        print(f"Auggie command: {AUGGIE_COMMAND}")
        print(f"CDOS path: {AUGGIE_CDOS_PATH}")
        print(f"CDOS path exists: {os.path.exists(AUGGIE_CDOS_PATH)}")

        # Test if auggie command is available
        try:
            result = subprocess.run([AUGGIE_COMMAND, "--version"], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"Auggie version: {result.stdout.strip()}")
            else:
                print(f"Auggie test failed: {result.stderr}")
        except FileNotFoundError:
            print(f"Warning: Auggie command '{AUGGIE_COMMAND}' not found in PATH")
        except Exception as e:
            print(f"Error testing Auggie: {e}")

    elif AI_PROVIDER == "openai":
        print(f"OpenAI model: {OPENAI_MODEL}")
        print(f"OpenAI client initialized: {openai_client is not None}")

    print()


def test_jira_connection():
    """Test Jira connection and list available projects"""
    try:
        # Test basic connection
        r = requests.get(f'{JIRA["base"]}/rest/api/3/myself', headers=_auth_header(), timeout=10)
        print(f"User info: {r.status_code}")
        if r.status_code == 200:
            print(f"Logged in as: {r.json().get('displayName', 'Unknown')}")

        # List projects
        r = requests.get(f'{JIRA["base"]}/rest/api/3/project', headers=_auth_header(), timeout=10)
        print(f"Projects: {r.status_code}")
        if r.status_code == 200:
            projects = r.json()
            print("Available projects:")
            for p in projects:
                print(f"  - Key: {p['key']}, Name: {p['name']}")
        else:
            print(f"Error: {r.text}")

    except Exception as e:
        print(f"Connection error: {e}")

if __name__ == "__main__":
    # Test AI provider configuration
    test_ai_provider()

    # Test Jira connection
    test_jira_connection()

    feature_text = """
    In notes.py for the iFrame containing charting.html, ensure the styling matches the rest of the app and does not override parent styles (themes).
    """
    print(json.dumps(draft_and_create(feature_text), indent=2))
