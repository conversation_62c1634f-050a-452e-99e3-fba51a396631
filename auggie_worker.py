#!/usr/bin/env python3
"""
Auggie Worker - Automated development workflow
Fetches Jira tickets, uses Auggie to generate code, creates branches and PRs
"""

import os
import sys
import json
import base64
import requests
import subprocess
import tempfile
import argparse
from typing import Dict, Any, Optional
from datetime import datetime

# Configuration
JIRA = {
    "base": "https://tradecraft1.atlassian.net",
    "email": "<EMAIL>",
    "token": "ATATT3xFfGF0Vs9ziGNK3A0Pp1JtKZ4tQuO5rInrIh9UvbYL_xM--S-CDaBYMXT2yYVihrls3oQO5WRd5B5Ep3HBlQoDm-TVG_YtYKhN_48vtqT_B5VEJGfrbwTQWGcpfHDtXC7f1iGmxxH7vnAnSrJ02S8Pp56odyGgynJrCADTF7xk-QUa2II=BA9D1004",
    "project": "CCS",
}

AUGGIE_CDOS_PATH = r"C:\Users\<USER>\PycharmProjects\CDOS"
AUGGIE_COMMAND = "auggie"
GITHUB_REPO = "https://github.com/alibby456/CDOS"


class JiraService:
    """Service for interacting with Jira API"""

    def __init__(self):
        self.base_url = JIRA["base"]
        self.email = JIRA["email"]
        self.token = JIRA["token"]

    def _auth_header(self) -> Dict[str, str]:
        """Generate authentication header for Jira API"""
        b = base64.b64encode(f'{self.email}:{self.token}'.encode()).decode()
        return {
            "Authorization": f"Basic {b}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

    def get_ticket(self, ticket_key: str) -> Dict[str, Any]:
        """Fetch a Jira ticket by key"""
        url = f"{self.base_url}/rest/api/3/issue/{ticket_key}"

        try:
            response = requests.get(url, headers=self._auth_header(), timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Failed to fetch Jira ticket {ticket_key}: {str(e)}")

    def format_ticket_for_prompt(self, ticket_data: Dict[str, Any]) -> str:
        """Format Jira ticket data into a prompt for Auggie"""
        fields = ticket_data.get("fields", {})

        summary = fields.get("summary", "No summary")
        description = fields.get("description", {})

        # Extract description text from ADF format
        description_text = self._extract_description_text(description)

        issue_type = fields.get("issuetype", {}).get("name", "Unknown")
        priority = fields.get("priority", {}).get("name", "Medium")
        status = fields.get("status", {}).get("name", "Unknown")

        # Get labels
        labels = fields.get("labels", [])
        labels_text = ", ".join(labels) if labels else "None"

        prompt = f"""
Please implement the following Jira ticket:

**Ticket:** {ticket_data.get("key", "Unknown")}
**Type:** {issue_type}
**Priority:** {priority}
**Status:** {status}
**Labels:** {labels_text}

**Summary:** {summary}

**Description:**
{description_text}

**Instructions:**
1. Analyze the requirements carefully
2. Implement the solution following best practices
3. Include proper error handling and logging
4. Add appropriate comments and documentation
5. Consider the existing CDOS codebase structure
6. Return the complete implementation with file paths

Please provide the implementation as complete, ready-to-use code files.
"""
        return prompt.strip()

    def _extract_description_text(self, description: Dict[str, Any]) -> str:
        """Extract plain text from Jira ADF (Atlassian Document Format)"""
        if not description or not isinstance(description, dict):
            return "No description provided"

        def extract_text_from_content(content_list):
            text_parts = []
            for item in content_list:
                if item.get("type") == "text":
                    text_parts.append(item.get("text", ""))
                elif item.get("type") == "paragraph" and "content" in item:
                    text_parts.append(extract_text_from_content(item["content"]))
                elif "content" in item:
                    text_parts.append(extract_text_from_content(item["content"]))
            return " ".join(text_parts)

        content = description.get("content", [])
        return extract_text_from_content(content) or "No description provided"


class AuggieService:
    """Service for interacting with Auggie"""

    def __init__(self):
        self.cdos_path = AUGGIE_CDOS_PATH
        self.command = AUGGIE_COMMAND

    def call_auggie(self, prompt: str) -> str:
        """Call Auggie with the given prompt and return the response"""
        temp_file = None
        try:
            # Verify CDOS directory exists
            if not os.path.exists(self.cdos_path):
                raise Exception(f"CDOS directory not found: {self.cdos_path}")

            print(f"Calling Auggie from CDOS directory: {self.cdos_path}")
            print(f"Prompt length: {len(prompt)} characters")

            # Write prompt to temporary file
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as f:
                f.write(prompt)
                temp_file = f.name

            # Use PowerShell to run auggie with instruction file
            powershell_cmd = f'cd "{self.cdos_path}"; {self.command} --print --instruction-file "{temp_file}"'

            result = subprocess.run(
                ["powershell", "-Command", powershell_cmd],
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout for complex implementations
            )

            if result.returncode == 0:
                print("Auggie completed successfully")
                response = result.stdout.strip()
                if response:
                    return response
                else:
                    raise Exception("Auggie returned empty response")
            else:
                print(f"Auggie error (return code {result.returncode})")
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")
                raise Exception(f"Auggie failed with return code {result.returncode}: {result.stderr}")

        except subprocess.TimeoutExpired:
            raise Exception("Auggie call timed out after 5 minutes")
        except FileNotFoundError:
            raise Exception("PowerShell not found. Make sure PowerShell is available.")
        except Exception as e:
            raise Exception(f"Error calling Auggie: {str(e)}")
        finally:
            # Clean up temporary file
            if temp_file and os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                except:
                    pass  # Ignore cleanup errors


class CodeProcessor:
    """Service for processing and saving code from Auggie responses"""

    def __init__(self, base_path: str = None):
        self.base_path = base_path or AUGGIE_CDOS_PATH

    def extract_and_save_code(self, auggie_response: str, ticket_key: str) -> list:
        """Extract code blocks from Auggie response and save to files"""
        saved_files = []

        # Look for code blocks in the response
        lines = auggie_response.split('\n')
        current_file = None
        current_code = []
        in_code_block = False

        for line in lines:
            # Check for file path indicators
            if line.strip().startswith('# File:') or line.strip().startswith('## File:'):
                current_file = line.split(':', 1)[1].strip()
                continue

            # Check for code block markers
            if line.strip().startswith('```'):
                if in_code_block:
                    # End of code block - save the file
                    if current_file and current_code:
                        file_path = self._save_code_file(current_file, '\n'.join(current_code), ticket_key)
                        if file_path:
                            saved_files.append(file_path)
                    current_code = []
                    in_code_block = False
                else:
                    # Start of code block
                    in_code_block = True
                continue

            # Collect code lines
            if in_code_block:
                current_code.append(line)

        # Handle case where there's no explicit file markers - save as single file
        if not saved_files and auggie_response.strip():
            # Try to determine file extension from content
            if 'def ' in auggie_response or 'import ' in auggie_response:
                ext = '.py'
            elif 'function ' in auggie_response or 'const ' in auggie_response:
                ext = '.js'
            else:
                ext = '.txt'

            filename = f"{ticket_key.lower().replace('-', '_')}_implementation{ext}"
            file_path = self._save_code_file(filename, auggie_response, ticket_key)
            if file_path:
                saved_files.append(file_path)

        return saved_files

    def _save_code_file(self, filename: str, content: str, ticket_key: str) -> Optional[str]:
        """Save code content to a file"""
        try:
            # Ensure filename is relative to base path
            if os.path.isabs(filename):
                file_path = filename
            else:
                file_path = os.path.join(self.base_path, filename)

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Save the file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            print(f"Saved code to: {file_path}")
            return file_path

        except Exception as e:
            print(f"Error saving file {filename}: {str(e)}")
            return None


class GitService:
    """Service for Git operations"""

    def __init__(self, repo_path: str = None):
        self.repo_path = repo_path or AUGGIE_CDOS_PATH

    def create_branch_and_commit(self, ticket_key: str, files: list, commit_message: str = None) -> str:
        """Create a new branch, commit files, and push to remote"""
        branch_name = f"feature/{ticket_key.lower()}"

        try:
            # Change to repo directory
            original_cwd = os.getcwd()
            os.chdir(self.repo_path)

            # Create and checkout new branch
            subprocess.run(["git", "checkout", "-b", branch_name], check=True, capture_output=True)
            print(f"Created and switched to branch: {branch_name}")

            # Add files
            for file_path in files:
                # Make path relative to repo root
                rel_path = os.path.relpath(file_path, self.repo_path)
                subprocess.run(["git", "add", rel_path], check=True, capture_output=True)

            # Commit changes
            if not commit_message:
                commit_message = f"Implement {ticket_key}: Automated implementation via Auggie"

            subprocess.run(["git", "commit", "-m", commit_message], check=True, capture_output=True)
            print(f"Committed changes with message: {commit_message}")

            # Push to remote
            subprocess.run(["git", "push", "-u", "origin", branch_name], check=True, capture_output=True)
            print(f"Pushed branch to remote: {branch_name}")

            return branch_name

        except subprocess.CalledProcessError as e:
            raise Exception(f"Git operation failed: {e}")
        finally:
            os.chdir(original_cwd)

    def create_pull_request(self, ticket_key: str, branch_name: str, ticket_data: Dict[str, Any]) -> str:
        """Create a pull request using GitHub CLI"""
        try:
            # Change to repo directory
            original_cwd = os.getcwd()
            os.chdir(self.repo_path)

            # Extract ticket info
            summary = ticket_data.get("fields", {}).get("summary", "No summary")

            # Create PR title and body
            pr_title = f"{ticket_key}: {summary}"
            pr_body = f"""
## Jira Ticket: {ticket_key}

**Summary:** {summary}

**Implementation:**
This PR implements the requirements specified in Jira ticket {ticket_key}.

**Generated by:** Auggie Worker (Automated Implementation)

**Jira Link:** {JIRA['base']}/browse/{ticket_key}
"""

            # Create pull request using GitHub CLI
            result = subprocess.run([
                "gh", "pr", "create",
                "--title", pr_title,
                "--body", pr_body,
                "--head", branch_name,
                "--base", "main"
            ], check=True, capture_output=True, text=True)

            pr_url = result.stdout.strip()
            print(f"Created pull request: {pr_url}")
            return pr_url

        except subprocess.CalledProcessError as e:
            raise Exception(f"Failed to create pull request: {e}")
        finally:
            os.chdir(original_cwd)


def main():
    """Main entry point for auggie_worker"""
    parser = argparse.ArgumentParser(description="Auggie Worker - Automated development workflow")
    parser.add_argument("ticket_key", help="Jira ticket key (e.g., CCS-123)")
    parser.add_argument("--dry-run", action="store_true", help="Run without creating branch/PR")
    parser.add_argument("--no-pr", action="store_true", help="Create branch but don't open PR")

    args = parser.parse_args()

    try:
        print(f"🎫 Processing Jira ticket: {args.ticket_key}")

        # Step 1: Fetch Jira ticket
        jira = JiraService()
        ticket_data = jira.get_ticket(args.ticket_key)
        print(f"✅ Fetched ticket: {ticket_data['fields']['summary']}")

        # Step 2: Format prompt
        prompt = jira.format_ticket_for_prompt(ticket_data)
        print(f"📝 Created prompt ({len(prompt)} characters)")
        print(f"📝 Created prompt {prompt}")

        # Step 3: Call Auggie
        auggie = AuggieService()
        response = auggie.call_auggie(prompt)
        print(f"🤖 Auggie generated response ({len(response)} characters)")

        # Step 4: Save code
        processor = CodeProcessor()
        saved_files = processor.extract_and_save_code(response, args.ticket_key)
        print(f"💾 Saved {len(saved_files)} files")

        if not args.dry_run and saved_files:
            # Step 5: Create Git branch and commit
            git = GitService()
            branch_name = git.create_branch_and_commit(args.ticket_key, saved_files)
            print(f"🌿 Created branch and committed: {branch_name}")

            # Step 6: Open pull request
            if not args.no_pr:
                pr_url = git.create_pull_request(args.ticket_key, branch_name, ticket_data)
                print(f"🔀 Created pull request: {pr_url}")
        else:
            if args.dry_run:
                print("🏃 Dry run completed - no Git operations performed")
            else:
                print("⚠️ No files saved - skipping Git operations")

        print(f"✨ Successfully processed {args.ticket_key}")

    except Exception as e:
        print(f"❌ Error processing ticket: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()