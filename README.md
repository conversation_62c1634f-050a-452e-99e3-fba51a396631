# Auggie Worker

Automated development workflow that fetches <PERSON>ra tickets, uses <PERSON>gie to generate code, creates Git branches, and opens pull requests.

## Features

- 🎫 **Jira Integration**: Fetches ticket details via Jira REST API
- 🤖 **Auggie Integration**: Uses locally installed Auggie with CDOS codebase context
- 💾 **Code Processing**: Extracts and saves code from Auggie responses
- 🌿 **Git Automation**: Creates branches, commits changes, and pushes to remote
- 🔀 **Pull Request Creation**: Opens PRs using GitHub CLI

## Prerequisites

1. **Python 3.7+** with required packages:
   ```bash
   pip install -r requirements.txt
   ```

2. **Auggie** installed and accessible from CDOS directory:
   ```bash
   cd C:\Users\<USER>\PycharmProjects\CDOS
   auggie --help  # Should work
   ```

3. **GitHub CLI** (for PR creation):
   ```bash
   gh auth login
   ```

4. **Git** configured with proper credentials

## Configuration

Update the configuration in `auggie_worker.py`:

```python
JIRA = {
    "base": "https://tradecraft1.atlassian.net",
    "email": "<EMAIL>", 
    "token": "your_jira_api_token",
    "project": "CCS",
}

AUGGIE_CDOS_PATH = r"C:\Users\<USER>\PycharmProjects\CDOS"
GITHUB_REPO = "https://github.com/alibby456/CDOS"
```

## Usage

### Basic Usage

Process a Jira ticket and create a full implementation:

```bash
python auggie_worker.py CCS-123
```

### Options

- `--dry-run`: Test the workflow without creating Git branches or PRs
- `--no-pr`: Create branch and commit but don't open a PR

```bash
# Test workflow without Git operations
python auggie_worker.py CCS-123 --dry-run

# Create branch but don't open PR
python auggie_worker.py CCS-123 --no-pr
```

## Workflow Steps

1. **Fetch Jira Ticket**: Retrieves ticket details using Jira REST API
2. **Format Prompt**: Converts ticket summary and description into Auggie prompt
3. **Call Auggie**: Executes Auggie with codebase context to generate implementation
4. **Save Code**: Extracts code blocks from Auggie response and saves to files
5. **Create Branch**: Creates new Git branch named `feature/ticket-key`
6. **Commit Changes**: Commits all generated files with descriptive message
7. **Push to Remote**: Pushes branch to GitHub repository
8. **Open Pull Request**: Creates PR with ticket details and implementation summary

## Example Output

```
🎫 Processing Jira ticket: CCS-32
✅ Fetched ticket: Test theme consistency across all supported Bootstrap themes
📝 Created prompt (834 characters)
Calling Auggie from CDOS directory: C:\Users\<USER>\PycharmProjects\CDOS
Prompt length: 834 characters
🤖 Auggie generated response (2976 characters)
💾 Saved 2 files:
  - C:\Users\<USER>\PycharmProjects\CDOS\test_theme_consistency.py
  - C:\Users\<USER>\PycharmProjects\CDOS\theme_test_config.json
🌿 Created branch and committed: feature/ccs-32
🔀 Created pull request: https://github.com/alibby456/CDOS/pull/123
✨ Successfully processed CCS-32
```

## Code Processing

The tool automatically extracts code from Auggie responses using these patterns:

- **File markers**: `# File: path/to/file.py` or `## File: path/to/file.py`
- **Code blocks**: Markdown code blocks with ````python`, ````javascript`, etc.
- **Auto-detection**: If no explicit markers, determines file type from content

## Error Handling

- **Jira API errors**: Invalid tickets, authentication issues
- **Auggie timeouts**: 5-minute timeout for complex implementations
- **Git conflicts**: Handles existing branches and merge conflicts
- **File system errors**: Directory creation and file writing issues

## Testing

Use the test script to verify workflow without calling Auggie:

```bash
python test_auggie_worker.py
```

This demonstrates the complete workflow using mock Auggie responses.

## Troubleshooting

### Common Issues

1. **"Auggie command not found"**
   - Ensure Auggie is installed and working from CDOS directory
   - Check `AUGGIE_CDOS_PATH` configuration

2. **"Failed to fetch Jira ticket"**
   - Verify Jira API token and permissions
   - Check ticket key format (e.g., CCS-123)

3. **"Git operation failed"**
   - Ensure Git is configured with proper credentials
   - Check if branch already exists

4. **"Failed to create pull request"**
   - Install and authenticate GitHub CLI: `gh auth login`
   - Verify repository permissions

### Debug Mode

Add debug prints by modifying the timeout and adding verbose output:

```python
# In AuggieService.call_auggie()
timeout=300,  # Increase for complex tickets
```

## Integration with Existing Workflow

This tool complements the existing `ticket_creator.py`:

1. **ticket_creator.py**: Creates Jira tickets from feature descriptions
2. **auggie_worker.py**: Implements existing Jira tickets automatically

Combined workflow:
```bash
# Create tickets from feature description
python ticket_creator.py

# Implement individual tickets
python auggie_worker.py CCS-123
python auggie_worker.py CCS-124
```
