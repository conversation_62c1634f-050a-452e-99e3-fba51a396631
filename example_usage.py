#!/usr/bin/env python3
"""
Example usage of Auggie Worker
"""

import subprocess
import sys

def run_command(cmd):
    """Run a command and print the result"""
    print(f"Running: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ Success!")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stderr:
            print(e.stderr)
        return False

def main():
    """Example usage scenarios"""
    
    print("🚀 Auggie Worker Example Usage\n")
    
    # Example 1: Dry run to test workflow
    print("1. Testing workflow with dry run:")
    if not run_command([sys.executable, "auggie_worker.py", "CCS-32", "--dry-run"]):
        print("   Fix configuration issues before proceeding\n")
        return
    
    print("\n" + "="*50 + "\n")
    
    # Example 2: List available tickets
    print("2. Available tickets in CCS project:")
    list_tickets_code = '''
import requests, base64
JIRA = {"base": "https://tradecraft1.atlassian.net", "email": "<EMAIL>", "token": "ATATT3xFfGF0Vs9ziGNK3A0Pp1JtKZ4tQuO5rInrIh9UvbYL_xM--S-CDaBYMXT2yYVihrls3oQO5WRd5B5Ep3HBlQoDm-TVG_YtYKhN_48vtqT_B5VEJGfrbwTQWGcpfHDtXC7f1iGmxxH7vnAnSrJ02S8Pp56odyGgynJrCADTF7xk-QUa2II=BA9D1004"}
b = base64.b64encode(f'{JIRA["email"]}:{JIRA["token"]}'.encode()).decode()
headers = {"Authorization": f"Basic {b}", "Accept": "application/json"}
r = requests.get(f'{JIRA["base"]}/rest/api/3/search?jql=project=CCS AND status="To Do"&maxResults=5', headers=headers)
print("Available tickets to implement:")
for issue in r.json().get("issues", []):
    print(f"  {issue['key']}: {issue['fields']['summary']}")
'''
    
    if not run_command([sys.executable, "-c", list_tickets_code]):
        print("   Could not fetch tickets\n")
        return
    
    print("\n" + "="*50 + "\n")
    
    # Example 3: Show what a full run would do
    print("3. Full workflow example (what would happen):")
    print("""
   To run a full implementation:
   
   # Process a ticket and create PR
   python auggie_worker.py CCS-32
   
   # Process without creating PR
   python auggie_worker.py CCS-32 --no-pr
   
   This would:
   ✅ Fetch ticket details from Jira
   ✅ Generate implementation using Auggie + CDOS context
   ✅ Save code files to CDOS directory
   ✅ Create Git branch: feature/ccs-32
   ✅ Commit and push changes
   ✅ Open pull request on GitHub
   """)
    
    print("\n🎯 Ready to use Auggie Worker!")
    print("   Start with: python auggie_worker.py TICKET-KEY --dry-run")

if __name__ == "__main__":
    main()
